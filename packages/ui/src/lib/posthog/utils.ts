import posthog from 'posthog-js';

// 给 trackEventParams 里的所有一级 key 加上 params_ 前缀
export const addParamsPrefix = (obj: Record<string, unknown>) => {
  return Object.fromEntries(Object.entries(obj).map(([key, value]) => [`params_${key}`, value]));
};

/**
 * 直接发送埋点事件，不依赖 React hook
 * 可以在非 React 环境下使用，比如工具函数、Promise 回调等
 */
export const sendTrackEvent = (eventName: string, properties: Record<string, unknown> = {}) => {
  try {
    // 检查 posthog 实例是否已初始化
    if (typeof window !== 'undefined' && posthog) {
      posthog.capture(eventName, addParamsPrefix(properties));
    } else {
      console.warn('PostHog not initialized or not in browser environment');
    }
  } catch (error) {
    console.error('Failed to send track event:', error);
  }
};

/**
 * 发送按钮点击事件
 */
export const sendButtonClickEvent = (
  buttonName: string,
  extraProps: Record<string, unknown> = {},
) => {
  sendTrackEvent('button_click', {
    ...addParamsPrefix(extraProps),
    button_name: buttonName,
  });
};

import { usePostHog } from 'posthog-js/react';
import { addParamsPrefix } from './utils';

/**
 * 发送埋点事件 Hook
 * 上报埋点事件统一用这个，不在 react 里的话可以使用 @/lib/util/posthog 中的 sendTrackEvent
 */
export const useTrackActions = () => {
  const posthog = usePostHog();

  const trackButtonClick = async (buttonName: string, extraProps: Record<string, unknown> = {}) => {
    // 给 trackEventParams 里的所有一级 key 加上 params_ 前缀
    posthog.capture('button_click', {
      ...addParamsPrefix(extraProps),
      button_name: buttonName,
    });
  };

  const trackEvent = async (eventName: string, properties: Record<string, unknown> = {}) => {
    posthog.capture(eventName, {
      ...addParamsPrefix(properties),
    });
  };

  return {
    trackButtonClick,
    trackEvent,
  };
};

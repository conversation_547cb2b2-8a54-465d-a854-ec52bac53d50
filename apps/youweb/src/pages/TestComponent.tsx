'use client';

import { But<PERSON>, ButtonWithTooltip } from '@repo/ui/components/ui/button';
import {
  ArrowRight,
  Check,
  ChevronDown,
  Download,
  Edit,
  Heart,
  Home,
  Mail,
  Plus,
  Search,
  Settings,
  Star,
  Trash,
  Upload,
  User,
  X,
} from 'lucide-react';
import { useState } from 'react';

export default function TestComponent() {
  const [loading, setLoading] = useState(false);

  const handleAsyncClick = async () => {
    await new Promise((resolve) => setTimeout(resolve, 2000));
    console.log('Async operation completed');
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen gap-8 p-8 bg-gray-50">
      <h1 className="text-3xl font-bold">Button Component Test</h1>

      {/* Test iconSize property */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Icon Size Tests</h2>
        <div className="flex flex-wrap gap-4">
          <Button>
            <Search />
            Default Icon Size
          </Button>
          <Button size="lg">
            <Search />
            12px Icon
          </Button>
          <Button iconSize={16}>
            <Search />
            16px Icon
          </Button>
          <Button iconSize={20}>
            <Search />
            20px Icon
          </Button>
          <Button iconSize={24}>
            <Search />
            24px Icon
          </Button>
          <Button iconSize={32}>
            <Search />
            32px Icon
          </Button>
        </div>
      </section>

      {/* Different button sizes with custom iconSize */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Button Sizes with Custom Icon Size</h2>
        <div className="flex flex-wrap items-center gap-4">
          <Button size="xxs" iconSize={8}>
            <Star />
            XXS + 8px
          </Button>
          <Button size="xs" iconSize={10}>
            <Star />
            XS + 10px
          </Button>
          <Button size="sm" iconSize={14}>
            <Star />
            SM + 14px
          </Button>
          <Button size="default" iconSize={18}>
            <Star />
            Default + 18px
          </Button>
          <Button size="lg" iconSize={24}>
            <Star />
            LG + 24px
          </Button>
        </div>
      </section>

      {/* Icon button variants with custom sizes */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Icon Button Variants</h2>
        <div className="flex flex-wrap items-center gap-4">
          <Button variant="icon" size="xxs">
            <Heart />
          </Button>
          <Button variant="icon" size="xs">
            <Heart />
          </Button>
          <Button variant="icon" size="sm">
            <Heart />
          </Button>
          <Button variant="icon" size="default">
            <Heart />
          </Button>
          <Button variant="icon" size="lg">
            <Heart />
          </Button>
          <Button variant="icon" size="default" iconSize={28}>
            <Heart />
          </Button>
          <Button variant="icon" size="default" iconSize={40}>
            <Heart />
          </Button>
        </div>
      </section>

      {/* Loading states with different icon sizes */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Loading States</h2>
        <div className="flex flex-wrap gap-4">
          <Button loading>Default Loading</Button>
          <Button loading iconSize={12}>
            Loading 12px
          </Button>
          <Button loading iconSize={20}>
            Loading 20px
          </Button>
          <Button loading iconSize={24}>
            Loading 24px
          </Button>
          <Button
            loading={loading}
            onClick={() => {
              setLoading(true);
              setTimeout(() => setLoading(false), 2000);
            }}
          >
            Click to Load
          </Button>
        </div>
      </section>

      {/* All button variants */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Button Variants</h2>
        <div className="flex flex-wrap gap-4">
          <Button variant="default">
            <Mail />
            Default
          </Button>
          <Button variant="secondary">
            <Settings />
            Secondary
          </Button>
          <Button variant="outline">
            <User />
            Outline
          </Button>
          <Button variant="destructive">
            <Trash />
            Destructive
          </Button>
          <Button variant="ghost">
            <Home />
            Ghost
          </Button>
        </div>
      </section>

      {/* Multiple icons in buttons */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Multiple Icons</h2>
        <div className="flex flex-wrap gap-4">
          <Button>
            <Download />
            Download
            <ChevronDown />
          </Button>
          <Button iconSize={20}>
            <Upload />
            Upload File
            <ArrowRight />
          </Button>
          <Button variant="outline" iconSize={14}>
            <Edit />
            Edit Document
            <ChevronDown />
          </Button>
        </div>
      </section>

      {/* Async buttons */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Async Operations</h2>
        <div className="flex flex-wrap gap-4">
          <Button async onClick={handleAsyncClick}>
            <Check />
            Async Default
          </Button>
          <Button async onClick={handleAsyncClick} iconSize={20}>
            <Check />
            Async 20px
          </Button>
          <Button async onClick={handleAsyncClick} iconSize={24}>
            <Check />
            Async 24px
          </Button>
        </div>
      </section>

      {/* ButtonWithTooltip */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Button with Tooltip</h2>
        <div className="flex flex-wrap gap-4">
          <ButtonWithTooltip tooltip="Add new item">
            <Plus />
          </ButtonWithTooltip>
          <ButtonWithTooltip tooltip="Delete item" iconSize={20}>
            <X />
          </ButtonWithTooltip>
          <ButtonWithTooltip tooltip="Settings" iconSize={24}>
            <Settings />
          </ButtonWithTooltip>
        </div>
      </section>

      {/* Disabled states */}
      <section className="w-full max-w-4xl">
        <h2 className="mb-4 text-xl font-semibold">Disabled States</h2>
        <div className="flex flex-wrap gap-4">
          <Button disabled>
            <Mail />
            Disabled Default
          </Button>
          <Button disabled variant="secondary" iconSize={20}>
            <Mail />
            Disabled Secondary
          </Button>
          <Button disabled variant="outline" iconSize={24}>
            <Mail />
            Disabled Outline
          </Button>
        </div>
      </section>
    </div>
  );
}

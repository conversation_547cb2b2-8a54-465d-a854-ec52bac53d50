import { isChinaMainlandTimezone } from '@repo/common';
import { SubscriptionStatusEnum } from '@repo/common/types/subscription/types';
import { Button } from '@repo/ui/components/ui/button';
import { useAtom } from 'jotai';
import { Mail } from 'lucide-react';
import { Link } from 'react-router-dom';
import { WechatHoverCard } from '@/components/contact/wechat-hover-card';
import { Discord } from '@/components/icon/discord';
import { Wechat } from '@/components/icon/wechat';
import { spaceAtom } from '@/hooks/useSpace';
import { useTranslation } from '@/hooks/useTranslation';
import { userAtom } from '@/hooks/useUser';
import { cn } from '@/utils/utils';

export interface AboutProps extends React.HTMLAttributes<HTMLDivElement> {}

export default function About({ className }: AboutProps) {
  const { t } = useTranslation('Settings');
  const [user] = useAtom(userAtom);
  const [space] = useAtom(spaceAtom);
  return (
    <div
      className={cn('flex flex-col items-center gap-6 rounded-2xl bg-background p-6', className)}
    >
      <p className="body text-center text-muted-foreground">
        Have questions?
        <br />
        {t('About.expectation')}
      </p>
      <div className="flex flex-col items-center gap-3">
        {space?.subscription?.status === SubscriptionStatusEnum.ACTIVE ||
        space?.subscription?.status === SubscriptionStatusEnum.TRIALING ? (
          user?.time_zone && isChinaMainlandTimezone(user?.time_zone) ? (
            <WechatHoverCard>
              <Button variant="outline" className="h-8 w-[240px]">
                <Wechat size={16} className="mr-2" />
                <span className="body">{t('About.joinWechat')}</span>
              </Button>
            </WechatHoverCard>
          ) : (
            <Link to={process.env.NEXT_PUBLIC_DISCORD_INVITE_LINK as string} target="_blank">
              <Button variant="outline" className="h-8 w-[240px]">
                <Discord size={16} className="mr-2" />
                <span className="body">{t('About.joinDiscord')}</span>
              </Button>
            </Link>
          )
        ) : null}
        {/* <Link href="https://x.com/YouMind_AI" target="_blank">
            <Button variant="outline" className="h-8 w-full rounded-full">
              <Facebook size={20} className="mr-2" />
              {t("followX")}
            </Button>
          </Link> */}
        <Link to="mailto:<EMAIL>">
          <Button variant="outline" className="h-8 w-[240px]" trackEventName="settings-feedback">
            <Mail size={16} className="mr-2" />
            <span className="body">{t('About.feedback')}</span>
          </Button>
        </Link>
      </div>

      <div className="body flex justify-center space-x-4 text-[hsla(var(--function-link))]">
        <a href="/terms" target="_blank" rel="noopener">
          {t('About.termsOfUse')}
        </a>
        <a href="/privacy" target="_blank" rel="noopener">
          {t('About.privacyNotice')}
        </a>
      </div>
    </div>
  );
}

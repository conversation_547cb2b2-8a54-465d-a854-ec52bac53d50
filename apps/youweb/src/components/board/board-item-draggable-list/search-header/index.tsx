import { BoardWithItemsVO } from '@repo/common/types/board/types';
import { Button } from '@repo/ui/components/ui/button';
import { useAtom } from 'jotai';
import { Plus, Search, X } from 'lucide-react';
import { useBoardItemTreeSearch } from '@/hooks/useBoardItemSearch';
import { boardDetailAtom } from '@/hooks/useBoards';
import { BoardAdderDialog } from '../../board-adder-dialog';

type SearchHeaderProps = {};

export const SearchHeader: React.FC<SearchHeaderProps> = () => {
  const [board] = useAtom(boardDetailAtom);
  const { search, setSearch } = useBoardItemTreeSearch();

  return (
    <div className="flex items-center justify-between gap-3 px-2">
      <div className="flex flex-1 items-center gap-[6px] rounded-[6px] bg-card-snips px-2 py-[5px]">
        <Search size={14} className="text-caption" />
        <input
          placeholder="Search"
          className="w-full bg-transparent text-sm font-normal placeholder:text-caption"
          type="text"
          value={search}
          onChange={(e) => setSearch(e.target.value)}
        />
        {search ? (
          <Button
            variant="icon"
            size="xxs"
            className="h-4 w-4 flex-shrink-0 rounded-full bg-card-snips text-muted-foreground hover:text-foreground"
            onClick={() => setSearch('')}
          >
            <X size={10} />
          </Button>
        ) : null}
      </div>
      <BoardAdderDialog board={board as BoardWithItemsVO} align="start" showNewGroup>
        <Button
          size="icon"
          variant="default"
          className="h-6 w-6 rounded-full"
          // disabled={loading}
        >
          <Plus size={16} />
        </Button>
      </BoardAdderDialog>
    </div>
  );
};

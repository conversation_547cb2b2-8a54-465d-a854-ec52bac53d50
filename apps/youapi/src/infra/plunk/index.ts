/**
 * Email Infrastructure Service - 邮件基础设施服务
 * 处理邮件发送和管理
 *
 * Migrated from:
 * - youapp/src/infra/email/index.ts
 */

import { Injectable, Logger } from '@nestjs/common';
import { isPreview } from '@repo/common';

import { fetchWithRetry } from '../../common/utils';

const PLUNK_API_KEY = process.env.PLUNK_API_KEY;
const PLUNK_API_BASE_URI = 'https://email.gooo.ai/api/v1';

// 发送邮件的参数接口
interface SendEmailPayload {
  subscribed?: boolean | null;
  from?: string | null;
  name?: string | null;
  reply?: string | null;
  // 最大 100
  to: string[] | string;
  subject: string;
  body: string;
  headers?: Record<string, string> | null;
}

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);

  // 封装 Plunk API 调用
  private async plunkApi(endpoint: string, method: 'POST' | 'PUT' | 'DELETE', body: object) {
    if (!PLUNK_API_KEY) {
      this.logger.error('PLUNK_API_KEY is not set. Skipping email sync.');
      return;
    }

    if (isPreview()) {
      this.logger.log(`预发无需同步邮件 - ${method} ${endpoint}:`, body);
      return;
    }

    try {
      const response = await fetchWithRetry(`${PLUNK_API_BASE_URI}${endpoint}`, {
        method,
        headers: {
          Authorization: `Bearer ${PLUNK_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errorText = await response.text();
        this.logger.error(`Plunk API error: ${response.status} ${response.statusText}`, errorText);
      }
      return await response.json();
    } catch (error) {
      this.logger.error('Failed to call Plunk API', error);
    }
  }

  // 内部函数：创建联系人
  private async _createContact(email: string, data: object) {
    return await this.plunkApi('/contacts', 'POST', {
      email,
      subscribed: true,
      data,
    });
  }

  // 内部函数：更新联系人
  private async _updateContact(email: string, data: object, subscribed?: boolean) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const payload: { email: string; data: object; subscribed?: boolean } = {
      email,
      data,
    };
    if (typeof subscribed === 'boolean') {
      payload.subscribed = subscribed;
    }
    return await this.plunkApi('/contacts', 'PUT', payload);
  }

  // 内部函数：删除联系人（软删除）
  // Plunk 的删除接口需要 contact id，但我们只有 email。
  // 因此我们采用软删除，即更新联系人状态为"deleted"并退订。
  private async _deleteContact(email: string) {
    return await this._updateContact(
      email,
      {
        deletedAt: new Date().toISOString(),
        status: 'deleted',
      },
      false,
    );
  }

  // 触发一个事件
  private async triggerEvent(email: string, eventName: string, data?: object) {
    return await this.plunkApi('/track', 'POST', {
      email,
      event: eventName,
      subscribed: true,
      data: data || {},
    });
  }

  // 用户注册时同步数据到邮件管理系统
  async syncUserRegistration(
    email: string,
    name: string,
    timeZone?: string,
    onboardStatus?: string,
  ) {
    const data = {
      name,
      timeZone,
      onboardStatus,
      registrationDate: new Date().toISOString(),
    };
    await this._createContact(email, data);
    await this.triggerEvent(email, 'user-signed-up', data);
  }

  // 用户偏好设置更新时同步数据
  async syncUserPreferenceUpdate(
    email: string,
    preferences: {
      displayLanguage?: string;
      aiResponseLanguage?: string;
      ai2ndResponseLanguage?: string;
      enableBilingual?: boolean;
      detectedLanguage?: string;
    },
  ) {
    await this._updateContact(email, {
      ...preferences,
      preferenceUpdateDate: new Date().toISOString(),
    });
  }

  // 用户订阅状态变更时同步数据
  async syncUserSubscriptionChange(
    email: string,
    subscriptionData: {
      status: string;
      source: string;
      plan?: string;
      nextBillingDate?: string;
      cancelAtPeriodEnd?: boolean;
    },
  ) {
    const data = {
      ...subscriptionData,
      subscriptionUpdateDate: new Date().toISOString(),
    };
    await this._updateContact(email, data);

    if (subscriptionData.status === 'active') {
      await this.triggerEvent(email, 'subscription-activated', data);
    }
  }

  // 用户删除时同步数据
  async syncUserDeletion(email: string) {
    await this._deleteContact(email);
  }

  // 发送邮件
  async send(payload: SendEmailPayload) {
    // 确保 to 字段是数组格式
    const recipients = Array.isArray(payload.to) ? payload.to : [payload.to];

    // 构建发送邮件的请求体
    const emailData: Record<string, unknown> = {
      to: recipients,
      subject: payload.subject,
      body: payload.body,
    };

    // 添加可选字段
    if (payload.subscribed !== undefined && payload.subscribed !== null) {
      emailData.subscribed = payload.subscribed;
    }
    if (payload.from) {
      emailData.from = payload.from;
    }
    if (payload.name) {
      emailData.name = payload.name;
    }
    if (payload.reply) {
      emailData.reply = payload.reply;
    }
    if (payload.headers) {
      emailData.headers = payload.headers;
    }

    return await this.plunkApi('/send', 'POST', emailData);
  }
}

/**
 * 路由配置文件
 * 统一的 preview/production 环境管理，支持 Pages 和 Worker 应用
 *
 * 注意：Worker 应用使用 Cloudflare 默认域名，无需自定义配置
 * 因为它们只通过 youroute 内部访问，用户不会直接看到这些域名
 *
 * Pages 域名可以通过 Wrangler CLI 自动发现，无需手动配置
 */

/**
 * 生成标准的 Worker 域名
 */
function createWorkerDomains(appName: string) {
  // Verified: account subdomain is 'youmind'
  // Using environment-specific worker names for proper deployment isolation
  const accountSubdomain = 'youmind';

  return {
    production: `${appName}-production.${accountSubdomain}.workers.dev`,
    preview: `${appName}-preview.${accountSubdomain}.workers.dev`,
  };
}

export interface AppRoute {
  /** 应用名称 */
  name: string;
  /** 匹配的路径前缀 */
  paths: string[];
  /** 应用类型 */
  type: 'pages' | 'worker' | 'custom';
  /** Pages 域名配置 */
  pages?: {
    production: string;
    preview: string;
  };
  /** Worker 域名配置 */
  worker?: {
    production: string;
    preview: string;
  };
  /** Custom 域名配置 (用于外部服务如 AWS ALB) */
  custom?: {
    production: string;
    preview: string;
  };
  /** 应用描述 */
  description: string;
}

/**
 * 应用路由配置
 * 统一的 preview/production 环境管理
 */
export const APP_ROUTES: AppRoute[] = [
  {
    name: 'youapi',
    type: 'custom',
    paths: ['/api', '/webhook', '/checkout-success', '/auth'],
    custom: {
      production: 'api-origin.youmind.com',
      preview: 'api-origin.youmind.com',
    },
    description: 'API 服务',
  },
  {
    name: 'youadmin',
    type: 'worker',
    paths: ['/admin'],
    worker: createWorkerDomains('youadmin'),
    description: '管理后台',
  },
  {
    name: 'youweb',
    type: 'pages',
    paths: ['/boards', '/snips', '/thoughts', '/static', '/settings'],
    pages: {
      production: 'youweb.pages.dev',
      preview: 'preview.youweb.pages.dev',
    },
    description: '看板管理、代码片段、思维笔记',
  },
  {
    name: 'youhome',
    type: 'worker',
    paths: [
      '/use-cases',
      '/overview',
      '/blog',
      '/sign-in',
      '/',
      '/privacy',
      '/terms',
      '/download-extension',
      '/email',
      '/pricing',
      '/blog-sitemap.xml',
      '/global-sitemap.xml',
      '/update-sitemap.xml',
      '/use-cases-sitemap.xml',
      '/404',
    ],
    worker: createWorkerDomains('youhome'),
    description: '使用案例、产品概览、用户认证',
  },
];

/**
 * 获取默认路由（当没有路由匹配时使用）
 */
export function getDefaultRoute(): AppRoute {
  const youwebRoute = APP_ROUTES.find((route) => route.name === 'youweb');
  if (!youwebRoute) {
    throw new Error('Default route (youweb) not found in APP_ROUTES');
  }
  return youwebRoute;
}

/**
 * 获取应用的目标域名（根据当前环境）
 */
export function getAppTargetDomain(route: AppRoute, environment: string): string {
  const env = environment === 'preview' ? 'preview' : 'production';

  if (route.type === 'pages' && route.pages) {
    return route.pages[env];
  }

  if (route.type === 'worker' && route.worker) {
    return route.worker[env];
  }

  if (route.type === 'custom' && route.custom) {
    return route.custom[env];
  }

  throw new Error(`Invalid route configuration for ${route.name}`);
}

/**
 * 根据路径查找匹配的应用路由
 */
export function findMatchingRoute(pathname: string): AppRoute | null {
  return APP_ROUTES.find((route) => route.paths.some((path) => pathname.startsWith(path))) || null;
}

/**
 * 获取所有路径列表（用于日志和调试）
 */
export function getAllPaths(): string[] {
  return APP_ROUTES.flatMap((route) => route.paths);
}

/**
 * 获取所有应用的环境信息（用于健康检查）
 */
export function getEnvironmentInfo(environment: string) {
  return APP_ROUTES.map((route) => ({
    name: route.name,
    type: route.type,
    paths: route.paths,
    targetDomain: getAppTargetDomain(route, environment),
  }));
}
